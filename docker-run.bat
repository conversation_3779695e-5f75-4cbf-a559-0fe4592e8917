@echo off
setlocal enabledelayedexpansion

REM NGAP Engine Docker Run Script (Windows)

echo Starting NGAP Engine container...

REM Set image name and tag
set IMAGE_NAME=ngap-engine
set IMAGE_TAG=latest
set FULL_IMAGE_NAME=!IMAGE_NAME!:!IMAGE_TAG!
set CONTAINER_NAME=ngap-engine-container

REM Check if image exists
docker images !IMAGE_NAME! | findstr !IMAGE_TAG! >nul
if !ERRORLEVEL! neq 0 (
    echo Image !FULL_IMAGE_NAME! does not exist!
    echo Please run build script first: docker-build.bat
    pause
    exit /b 1
)

REM Stop and remove existing container
docker ps -a | findstr !CONTAINER_NAME! >nul
if !ERRORLEVEL! equ 0 (
    echo Stopping and removing existing container...
    docker stop !CONTAINER_NAME! >nul 2>&1
    docker rm !CONTAINER_NAME! >nul 2>&1
)

REM Run new container
echo Starting new container: !CONTAINER_NAME!
docker run -d --name !CONTAINER_NAME! -p 5000:5000 --restart unless-stopped !FULL_IMAGE_NAME!

REM Check container status
if !ERRORLEVEL! equ 0 (
    echo Container started successfully!
    echo.
    echo Container info:
    docker ps | findstr !CONTAINER_NAME!
    echo.
    echo API service: http://localhost:5000
    echo Health check: http://localhost:5000/health
    echo.
    echo Common commands:
    echo    View logs: docker logs !CONTAINER_NAME!
    echo    Stop container: docker stop !CONTAINER_NAME!
    echo    Enter container: docker exec -it !CONTAINER_NAME! /bin/bash
    echo.
    echo Using Docker Compose:
    echo    Start: docker-compose up -d
    echo    Stop: docker-compose down
) else (
    echo Container start failed!
    pause
    exit /b 1
)

echo Press any key to exit...
pause >nul
