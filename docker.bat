@echo off
setlocal enabledelayedexpansion

set IMAGE_NAME=ngap-engine
set CONTAINER_NAME=ngap-engine-container

if "%1"=="build" goto build
if "%1"=="run" goto run
if "%1"=="stop" goto stop
if "%1"=="logs" goto logs

:help
echo NGAP Engine Docker Management Script
echo.
echo Usage:
echo   docker.bat build    - Build Docker image
echo   docker.bat run      - Run container
echo   docker.bat stop     - Stop container
echo   docker.bat logs     - View container logs
echo.
goto end

:build
echo Building NGAP Engine Docker image...
docker build -t !IMAGE_NAME! .
if !ERRORLEVEL! equ 0 (
    echo Build successful!
    docker images !IMAGE_NAME!
) else (
    echo Build failed!
)
goto end

:run
echo Starting NGAP Engine container...
docker stop !CONTAINER_NAME! >nul 2>&1
docker rm !CONTAINER_NAME! >nul 2>&1
docker run -d --name !CONTAINER_NAME! -p 5000:5000 !IMAGE_NAME!
if !ERRORLEVEL! equ 0 (
    echo Container started successfully!
    echo API: http://localhost:5000
    echo Health: http://localhost:5000/health
) else (
    echo Failed to start container!
)
goto end

:stop
echo Stopping container...
docker stop !CONTAINER_NAME!
docker rm !CONTAINER_NAME!
goto end

:logs
docker logs -f !CONTAINER_NAME!
goto end

:end
pause
