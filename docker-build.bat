@echo off
setlocal enabledelayedexpansion

REM NGAP Engine Docker Build Script (Windows)

echo Starting NGAP Engine Docker image build...

REM Set image name and tag
set IMAGE_NAME=ngap-engine
set IMAGE_TAG=latest
set FULL_IMAGE_NAME=!IMAGE_NAME!:!IMAGE_TAG!

REM Build Docker image
echo Building image: !FULL_IMAGE_NAME!
docker build -t !FULL_IMAGE_NAME! .

REM Check build result
if !ERRORLEVEL! equ 0 (
    echo Docker image build successful!
    echo Image name: !FULL_IMAGE_NAME!

    REM Show image info
    echo.
    echo Image details:
    docker images !IMAGE_NAME!

    echo.
    echo To run container use:
    echo    docker-run.bat
    echo    or:
    echo    docker run -p 5000:5000 !FULL_IMAGE_NAME!
    echo.
    echo Using Docker Compose:
    echo    docker-compose up -d
) else (
    echo Docker image build failed!
    pause
    exit /b 1
)

echo Build completed. Press any key to exit...
pause >nul
