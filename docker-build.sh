#!/bin/bash

# NGAP Engine Docker构建脚本

echo "开始构建NGAP Engine Docker镜像..."

# 设置镜像名称和标签
IMAGE_NAME="ngap-engine"
IMAGE_TAG="latest"
FULL_IMAGE_NAME="${IMAGE_NAME}:${IMAGE_TAG}"

# 构建Docker镜像
echo "构建镜像: ${FULL_IMAGE_NAME}"
docker build -t ${FULL_IMAGE_NAME} .

# 检查构建结果
if [ $? -eq 0 ]; then
    echo "✅ Docker镜像构建成功!"
    echo "镜像名称: ${FULL_IMAGE_NAME}"
    
    # 显示镜像信息
    echo ""
    echo "镜像详情:"
    docker images ${IMAGE_NAME}
    
    echo ""
    echo "🚀 使用以下命令运行容器:"
    echo "   ./docker-run.sh"
    echo "   或者:"
    echo "   docker run -p 5000:5000 ${FULL_IMAGE_NAME}"
else
    echo "❌ Docker镜像构建失败!"
    exit 1
fi
