#%%
import requests

url = "http://localhost:5000/run-dss-with-strategy"
payload = {
    "dssFilePath": "D:/CETWorkSpace/ngap-server/data/21/18/simulation_script/simulationScript.dss",
    "json_data":'''

    {"ACTIVE_POWER_VOLTAGE_CONTROL":{"REACTIVE_POWER_VOLTAGE_CONTROL":[{"name":"generator_1","pv_s":30,"v_deadzone_ratio":0.02,"v_upper_limit":242,"v_lower_limit":198},{"name":"generator_2","pv_s":30,"v_deadzone_ratio":0.02,"v_upper_limit":242,"v_lower_limit":198},{"name":"generator_3","pv_s":30,"v_deadzone_ratio":0.02,"v_upper_limit":242,"v_lower_limit":198},{"name":"generator_4","pv_s":30,"v_deadzone_ratio":0.02,"v_upper_limit":242,"v_lower_limit":198},{"name":"generator_5","pv_s":30,"v_deadzone_ratio":0.02,"v_upper_limit":242,"v_lower_limit":198},{"name":"generator_6","pv_s":30,"v_deadzone_ratio":0.02,"v_upper_limit":242,"v_lower_limit":198},{"name":"generator_7","pv_s":30,"v_deadzone_ratio":0.02,"v_upper_limit":242,"v_lower_limit":198},{"name":"generator_8","pv_s":30,"v_deadzone_ratio":0.02,"v_upper_limit":242,"v_lower_limit":198},{"name":"generator_9","pv_s":30,"v_deadzone_ratio":0.02,"v_upper_limit":242,"v_lower_limit":198},{"name":"generator_10","pv_s":30,"v_deadzone_ratio":0.02,"v_upper_limit":242,"v_lower_limit":198}]}

    '''

}
headers = {
    "Content-Type": "application/json"
}

response = requests.post(url, json=payload, headers=headers)
print(response.json())
#%%
