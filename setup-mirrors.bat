@echo off

echo Setting up Docker registry mirrors...
echo.
echo Please follow these steps:
echo.
echo 1. Open Docker Desktop
echo 2. Click Settings (gear icon in top-right)
echo 3. Go to "Docker Engine" tab
echo 4. Replace the JSON configuration with:
echo.
echo {
echo   "builder": {
echo     "gc": {
echo       "defaultKeepStorage": "20GB",
echo       "enabled": true
echo     }
echo   },
echo   "experimental": false,
echo   "registry-mirrors": [
echo     "https://docker.mirrors.ustc.edu.cn",
echo     "https://hub-mirror.c.163.com",
echo     "https://mirror.baidubce.com",
echo     "https://dockerproxy.com"
echo   ]
echo }
echo.
echo 5. Click "Apply & Restart"
echo 6. Wait for <PERSON><PERSON> to restart
echo 7. Run build.bat again
echo.

pause
