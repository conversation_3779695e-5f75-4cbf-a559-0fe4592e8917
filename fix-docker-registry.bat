@echo off
setlocal enabledelayedexpansion

echo Fixing Docker registry configuration...

REM Create Docker daemon.json with multiple registry mirrors
set DOCKER_CONFIG_DIR=%USERPROFILE%\.docker
if not exist "%DOCKER_CONFIG_DIR%" mkdir "%DOCKER_CONFIG_DIR%"

echo Creating daemon.json with registry mirrors...
(
echo {
echo   "registry-mirrors": [
echo     "https://docker.mirrors.ustc.edu.cn",
echo     "https://hub-mirror.c.163.com",
echo     "https://mirror.baidubce.com"
echo   ],
echo   "insecure-registries": [],
echo   "debug": false,
echo   "experimental": false
echo }
) > "%DOCKER_CONFIG_DIR%\daemon.json"

echo.
echo Docker registry configuration updated.
echo Please restart Docker Desktop and try building again.
echo.
echo Alternative: Use official Docker Hub directly
echo Set Docker Desktop to use no registry mirrors in Settings.
echo.
pause
