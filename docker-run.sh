#!/bin/bash

# NGAP Engine Docker运行脚本

echo "启动NGAP Engine容器..."

# 设置镜像名称和标签
IMAGE_NAME="ngap-engine"
IMAGE_TAG="latest"
FULL_IMAGE_NAME="${IMAGE_NAME}:${IMAGE_TAG}"
CONTAINER_NAME="ngap-engine-container"

# 检查镜像是否存在
if ! docker images ${IMAGE_NAME} | grep -q ${IMAGE_TAG}; then
    echo "❌ 镜像 ${FULL_IMAGE_NAME} 不存在!"
    echo "请先运行构建脚本: ./docker-build.sh"
    exit 1
fi

# 停止并删除已存在的容器
if docker ps -a | grep -q ${CONTAINER_NAME}; then
    echo "停止并删除已存在的容器..."
    docker stop ${CONTAINER_NAME} 2>/dev/null
    docker rm ${CONTAINER_NAME} 2>/dev/null
fi

# 运行新容器
echo "启动新容器: ${CONTAINER_NAME}"
docker run -d \
    --name ${CONTAINER_NAME} \
    -p 5000:5000 \
    --restart unless-stopped \
    ${FULL_IMAGE_NAME}

# 检查容器状态
if [ $? -eq 0 ]; then
    echo "✅ 容器启动成功!"
    echo ""
    echo "容器信息:"
    docker ps | grep ${CONTAINER_NAME}
    echo ""
    echo "🌐 API服务地址: http://localhost:5000"
    echo ""
    echo "📋 常用命令:"
    echo "   查看日志: docker logs ${CONTAINER_NAME}"
    echo "   停止容器: docker stop ${CONTAINER_NAME}"
    echo "   进入容器: docker exec -it ${CONTAINER_NAME} /bin/bash"
else
    echo "❌ 容器启动失败!"
    exit 1
fi
