@echo off
setlocal enabledelayedexpansion

echo Building NGAP Engine with official Docker Hub...

set IMAGE_NAME=ngap-engine
set IMAGE_TAG=latest
set FULL_IMAGE_NAME=!IMAGE_NAME!:!IMAGE_TAG!

echo.
echo Step 1: Pulling base image from official Docker Hub...
docker pull python:3.11-slim

if !ERRORLEVEL! neq 0 (
    echo Failed to pull base image. Trying alternative approach...
    echo.
    echo Please check your Docker Desktop settings:
    echo 1. Open Docker Desktop
    echo 2. Go to Settings ^> Docker Engine
    echo 3. Remove or comment out registry-mirrors configuration
    echo 4. Apply and restart Docker Desktop
    echo.
    pause
    exit /b 1
)

echo.
echo Step 2: Building application image...
docker build -t !FULL_IMAGE_NAME! .

if !ERRORLEVEL! equ 0 (
    echo.
    echo Build successful!
    echo Image: !FULL_IMAGE_NAME!
    echo.
    docker images !IMAGE_NAME!
    echo.
    echo To run the container:
    echo docker run -p 5000:5000 !FULL_IMAGE_NAME!
) else (
    echo.
    echo Build failed!
    echo Please check the error messages above.
)

echo.
echo Press any key to exit...
pause >nul
