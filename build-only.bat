@echo off
setlocal enabledelayedexpansion

echo Building NGAP Engine Docker image...

set IMAGE_NAME=ngap-engine
set IMAGE_TAG=latest
set FULL_IMAGE_NAME=!IMAGE_NAME!:!IMAGE_TAG!

echo Building image: !FULL_IMAGE_NAME!
docker build -t !FULL_IMAGE_NAME! .

if !ERRORLEVEL! equ 0 (
    echo.
    echo Build successful!
    echo Image: !FULL_IMAGE_NAME!
    echo.
    docker images !IMAGE_NAME!
) else (
    echo.
    echo Build failed!
)

echo.
echo Press any key to exit...
pause >nul
