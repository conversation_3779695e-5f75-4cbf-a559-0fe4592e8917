import opendssdirect as dss
import math
from typing import TypedDict
import matplotlib.pyplot as plt
import csv

# In[]
class PVsystemStrategyParams(TypedDict):
    """
    光伏无功-电压控制策略参数

    :param name: 光伏标识
    :param v_upper_limit: 电压上限
    :param v_lower_limit: 电压下限
    :param v_deadzone_ratio: 死区比
    :pv_s:额定容量
    """

    name: str
    v_upper_limit: float
    v_lower_limit: float
    v_deadzone_ratio: float
    pv_s: float


# 光伏设备的策略参数组，后续改为读取json文件
# params_list: list[PVsystemStrategyParams] = [
#     {
#         "name": "generator_1",
#         "v_upper_limit": 242.0,
#         "v_lower_limit": 198.0,
#         "v_deadzone_ratio": 0.05,
#         "pv_s": 30,
#     }
# ]


def get_bus_v_base(bus_name: str) -> float:
    """
    获取指定并网点的额定电压

    :param bus_name: 并网点标识
    :return: 额定电压(V)
    """

    dss.Circuit.SetActiveBus(bus_name)
    kVBase = dss.Bus.kVBase()
    return kVBase * 1000


def set_pv_q(pv_name: str, q_value: float):
    """
    设置指定光伏设备的无功功率

    :param pv_name: 光伏设备标识
    :param q_value: 要设定的无功功率
    """

    dss.Circuit.SetActiveElement(f'Generator.{pv_name}')
    dss.Text.Command(f"Edit Generator.{pv_name} kvar={q_value}")


def regulate_voltage(params: PVsystemStrategyParams, bus_voltage):
    """
    利用光伏无功电压调控
    """

    pv_name = params["name"]

    # 设置当前活动元素为指定的光伏系统
    dss.Circuit.SetActiveElement(f'Generator.{pv_name}')
    if not dss.CktElement.Name():
        raise ValueError(f"Generator '{pv_name}' not found in the circuit.")

    # 获取光伏系统连接的母线名称
    bus_name = dss.CktElement.BusNames()[0].split('.')[0]

    # bus_voltage = get_bus_voltage(pv_name)
    bus_v_base = get_bus_v_base(bus_name)
    # print(bus_v_base)

    v_deadzone_ratio = params["v_deadzone_ratio"]
    v_deadzone_upper_limit = bus_v_base * (1 + v_deadzone_ratio)
    v_deadzone_lower_limit = bus_v_base * (1 - v_deadzone_ratio)
    # print(bus_voltage)
    print(v_deadzone_upper_limit)
    print(v_deadzone_lower_limit )

    if v_deadzone_lower_limit < bus_voltage < v_deadzone_upper_limit:
        q_adjust = 0

    v_upper_limit = params["v_upper_limit"]
    v_lower_limit = params["v_lower_limit"]

    # 有功功率 (kW)
    pv_p = dss.CktElement.Powers()[0]
    # 获取额定电流
    # normal_amps = dss.CktElement.NormalAmps()
    # 获取当前母线电压
    dss.Circuit.SetActiveBus(bus_name)
    # bus_vmag = dss.Bus.VMagAngle()[0]  # 电压幅值

    # 计算额定容量 (kVA)
    pv_s = params['pv_s']

    q_max = math.sqrt(math.pow(pv_s, 2) - math.pow(pv_p, 2))
    q_min = -q_max

    if bus_voltage > v_deadzone_upper_limit:
        a = v_deadzone_upper_limit - bus_voltage
        b = v_deadzone_upper_limit - v_upper_limit
        ratio = min(1, a / b)
        set_pv_q(pv_name, q_min * ratio)
        q_adjust = q_min * ratio
    elif bus_voltage < v_deadzone_lower_limit:
        a = v_deadzone_lower_limit - bus_voltage
        b = v_deadzone_lower_limit - v_lower_limit
        ratio = min(1, a / b)
        set_pv_q(pv_name, -q_max * ratio)
        q_adjust = q_max * ratio

    return q_adjust

pv_name = params_list[0]["name"]

# 调控前电压
dss.Monitors.Name(f'monitor_{pv_name}_VI')
voltages_before = dss.Monitors.Channel(1)

dss.Text.Command("Set mode=time number=1 stepsize=15m")
# dss.Monitors.Name(f'monitor_{pv_name}_VI')
voltages_after = []
q = []
q_ = 0
pv2_kw = []
pv2_m = []
load_1_kw = []
# 获取光伏设备连接的母线名称
dss.Circuit.SetActiveElement(f'Generator.{pv_name}')
bus_name = dss.CktElement.BusNames()[0].split('.')[0]

# 执行控制策略并记录控制后的电压
for t in range(96):
    for i in dss.Loads.AllNames():
        dss.Circuit.SetActiveElement(i)
        dss.LoadShape.Name(f'LoadShape_{i}')
        # 放大负荷
        load_kw = dss.LoadShape.PMult()[t]
        dss.Text.Command(f"Edit load.{i} kw={load_kw}")
        dss.Text.Command(f"Edit load.{i} kvar=0.2")
        # if i=='load_1':
        #     print(load_kw)

    for i in dss.Generators.AllNames():
        if i == f'{pv_name}':
            for params in params_list:
                q_ = regulate_voltage(params, voltages_before[t])
                break
        else:
            name = dss.Generators.Name()
            rated_power_kva = dss.Generators.kVARated()
            # rated_power_kva=30
            dss.LoadShape.Name(f'LoadShape_{i}')
            # 放大光伏
            pv_t = dss.LoadShape.PMult()[t] * rated_power_kva
            dss.Text.Command(f"Edit Generator.{i} kw={pv_t}")
            # if i == 'pvsystem_2':
            #     pv2_kw.append(pv_t)

    q.append(q_)
    dss.Text.Command("Calcv")
    dss.Solution.Solve()
    dss.Monitors.SaveAll()

    # dss.Generators.Name("pvSystem_2")
    # pv1_kw = dss.Generators.kW()

    # dss.LoadShape.Name('pvLoadShape_pvSystem_2')
    # # l2=dss.LoadShape.PMult()

    # dss.Monitors.Name('monitor_pvSystem_2_PQ')
    # p2_kw = dss.Monitors.Channel(1)[-1] + dss.Monitors.Channel(3)[-1] + dss.Monitors.Channel(5)[-1]
    # pv2_m.append(p2_kw)

    # dss.Monitors.Name('monitor_load_1_PQ')
    # load_p=dss.Monitors.Channel(1)[-1]
    # load_1_kw.append(load_p)

    dss.Monitors.Name(f'monitor_{pv_name}_VI')
    voltages = dss.Monitors.Channel(1)[-1]
    voltages_after.append(voltages)

time_steps = list(range(96))
plt.figure(figsize=(10, 6))

# print(voltages_before)
# print(voltages_after)

plt.plot(time_steps, voltages_before, label='Before Control')
plt.plot(time_steps, voltages_after, label='After Control')
plt.xlabel('Time Step')
plt.ylabel('Voltage (V)')
plt.title('Voltage Comparison Before and After PV Control')
plt.legend()
plt.grid(True)
plt.show()

# raise ValueError('wrong')

# 输出仿真结果
full_path = output_dir + f'output.csv'

# 创建新的时间序列
new_data = []
for hour in range(24):
    for seconds in [900, 1800, 2700, 0]:
        if seconds == 0 and hour < 23:
            actual_hour = hour + 1
        elif seconds == 0 and hour == 23:
            actual_hour = 24
        else:
            actual_hour = hour
        new_data.append([actual_hour, float(f"{seconds:.5f}")])

# 确保q数组长度为96
if len(q) < 96:
    # 如果q长度不足，使用最后一个值填充到96个
    last_q = q[-1] if q else 0  # 如果q为空，使用0
    q.extend([last_q] * (96 - len(q)))
elif len(q) > 96:
    # 如果q长度超过96，只取前96个
    q = q[:96]

# 确保电压数据长度正确
voltages_before_new = voltages_before[:96]
voltages_after_new = voltages_after[:96]
q_new = q[:96]

# 组合数据
combined_data = []
for i in range(len(new_data)):
    hour, t = new_data[i]
    row = [hour, t, voltages_before_new[i], voltages_after_new[i], q_new[i]]
    combined_data.append(row)

with open(full_path, 'w', newline='', encoding='utf-8') as f:
    writer = csv.writer(f)
    writer.writerow(['Hour', 't(sec)', 'Voltage Before Control', 'Voltage After Control', 'q'])
    writer.writerows(combined_data)
